# Complete Authentication Flow Documentation

## Overview

This document provides a comprehensive breakdown of the authentication system, detailing every function, component, and flow in the application. The system uses AWS Cognito with AWS Amplify, implemented using Next.js 13+ App Router with server-side rendering and server actions.

### Key Architecture Decisions

- **Hybrid Token Management**: Server-side custom JWT sessions for application state + Cognito tokens for AWS API calls
- **Custom Token Storage**: In-memory server-side storage for Amplify operations to avoid SSR issues
- **Environment-Based API Authentication**: Uses environment variables for external API calls with refresh tokens
- **Comprehensive Security**: CSRF protection, secure cookies, rate limiting, and security headers

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Token Management Strategy](#token-management-strategy)
3. [Authentication Flow Diagrams](#authentication-flow-diagrams)
4. [Detailed Function Analysis](#detailed-function-analysis)
5. [Component Breakdown](#component-breakdown)
6. [API Integration](#api-integration)
7. [Security Implementation](#security-implementation)
8. [Session Management](#session-management)
9. [Error Handling](#error-handling)
10. [Debug and Testing](#debug-and-testing)

## System Architecture

```mermaid
graph TB
    subgraph "Client Side"
        A[Sign-in Form] --> B[useCSRFToken Hook]
        A --> C[useActionState Hook]
        D[Sign-up Form] --> B
        D --> C
        E[Verify Form] --> B
        E --> C
        F[Logout Button] --> G[logout action]
        FP[ForgotPassword Form] --> B
        FP --> RM1[React Query Mutation]
        RP[ResetPassword Form] --> B
        RP --> RM2[React Query Mutation]
        SS[SessionStorage] --> RP
    end

    subgraph "Server Actions"
        H[authenticateUser]
        I[registerUser]
        J[verifyEmail]
        G[logout]
        K[getSession]
        L[getCSRFToken]
        IPR[initiatePasswordReset]
        CPR[completePasswordReset]
    end

    subgraph "Session Management"
        M[createSession]
        N[setSessionCookie]
        O[verifySession]
        P[clearSession]
        Q[generateCSRFToken]
        R[verifyCSRFToken]
    end

    subgraph "Middleware"
        S[Route Protection]
        T[Security Headers]
        U[Rate Limiting]
        V[CSRF Protection]
    end

    subgraph "AWS Services"
        W[AWS Cognito]
        X[Amplify Auth]
        Y[ServerSideTokenStorage]
        EMAIL[Email Service]
    end

    subgraph "External APIs"
        Z[QBraid API]
        AA[Third-party Services]
    end

    C --> H
    C --> I
    C --> J
    RM1 --> IPR
    RM2 --> CPR
    H --> M
    H --> N
    H --> X
    I --> X
    J --> X
    IPR --> X
    CPR --> X
    X --> W
    X --> Y
    W --> EMAIL
    S --> O
    B --> L
    L --> Q
    H --> R
    I --> R
    J --> R
    IPR --> R
    CPR --> R
    FP --> SS
    Z --> AA
```

## Token Management Strategy

### Dual Token System with Redis Storage

The application implements a sophisticated dual token system with enterprise-grade Redis storage for scalability and security:

#### 1. **Application Sessions (Serverless-Ready)**

- **Purpose**: Internal application state and route protection
- **Storage**: Redis-based with lightweight cookie session IDs (serverless-compatible)
- **Lifetime**: 7 days with automatic refresh
- **Security**: Redis TTL, secure session isolation, automatic fallback to JWT cookies

#### 2. **Cognito Tokens (Serverless-Ready)**

- **Purpose**: AWS Cognito authentication and external API calls
- **Storage**: Redis-based secure storage linked to session IDs
- **Lifetime**: 1 hour expiration with automatic Amplify-managed refresh
- **Security**: Redis TTL, encrypted storage, session-based isolation, automatic cookie fallback

#### 3. **Redis Token Storage (Enterprise)**

- **Purpose**: Scalable, persistent token storage for serverless environments
- **Implementation**: `RedisTokenStorage` class with automatic fallback
- **Features**: TTL support, connection pooling, health monitoring, graceful degradation
- **Security**: TLS encryption, authentication, secure key prefixing

#### 4. **External API Authentication**

- **Purpose**: Third-party API calls (QBraid, etc.)
- **Method**: Bearer token authorization + email headers from Redis-stored tokens
- **Fallback**: Environment variables for development/testing
- **Security**: Automatic token injection, no client-side exposure, Redis-backed persistence

### Serverless Token Flow Architecture

```mermaid
graph TB
    subgraph "Client Authentication"
        A[User Login] --> B[Cognito Authentication]
        B --> C[Cognito Tokens Generated]
    end

    subgraph "Serverless Session Management"
        C --> D[ServerSideTokenStorage - Redis]
        D --> E[Session ID Generated]
        E --> F[Session Data Stored in Redis]
        F --> G[Lightweight Session ID Cookie]
        C --> H[Cognito Tokens Stored in Redis by Session ID]
    end

    subgraph "Stateless API Calls"
        I[Internal Routes] --> J[Session ID from Cookie]
        J --> K[Redis Session Validation]
        L[External APIs] --> M[Session-Aware Token Retrieval]
        M --> N[Bearer Token + Email Headers]
        O[AWS Services] --> P[Redis Token Retrieval]
    end

    subgraph "Fallback Mode"
        Q[Redis Unavailable] --> R[JWT Cookie Mode]
        R --> S[Traditional Cookie Storage]
    end

    G --> I
    H --> L
    N --> T[QBraid API]
    K --> U[Allow/Deny Access]
```

### Serverless Session Management Benefits

#### **Why Redis for Serverless?**

1. **Stateless Functions**: Serverless functions are stateless - cookies may not persist properly across invocations
2. **Horizontal Scaling**: Redis allows session sharing across multiple function instances
3. **Performance**: Faster session lookup compared to database queries
4. **Reliability**: Built-in TTL and automatic cleanup
5. **Security**: Centralized session management with proper isolation

## Authentication Flow Diagrams

### 1. Complete Sign-In Flow

```mermaid
sequenceDiagram
    participant U as User
    participant SF as SignInForm
    participant CH as useCSRFToken Hook
    participant API as /api/csrf-token
    participant SA as authenticateUser Action
    participant SS as Session Service
    participant AMP as AWS Amplify
    participant COG as AWS Cognito
    participant MW as Middleware
    participant R as Router

    Note over U,R: Initial Page Load
    U->>SF: Navigate to /auth/signin
    SF->>CH: useCSRFToken()
    CH->>API: GET /api/csrf-token
    API->>SS: generateCSRFToken()
    SS->>API: Return token
    API->>CH: Return {csrfToken}
    CH->>SF: Provide csrfToken
    SF->>U: Render form with CSRF token

    Note over U,R: User Authentication
    U->>SF: Enter email & password
    U->>SF: Submit form
    SF->>SA: formAction(formData + csrfToken)

    Note over SA: Server Action Processing
    SA->>SA: ensureAmplifyConfig()
    SA->>SS: verifyCSRFToken(csrfToken)
    SS->>SA: Return validation result
    SA->>SA: Validate email & password
    SA->>AMP: signIn({username, password})
    AMP->>COG: Authenticate user
    COG->>AMP: Return auth result
    AMP->>SA: Return {isSignedIn, nextStep}

    alt Authentication Successful
        Note over SA: Token & Session Management
        SA->>AMP: getCurrentUser()
        AMP->>SA: Return user details
        SA->>AMP: fetchAuthSession()
        AMP->>SA: Return tokens (access, id)
        SA->>SA: Store tokens in ServerSideTokenStorage
        SA->>SS: setCognitoTokenCookies({accessToken, idToken})
        SS->>SA: Tokens stored in secure cookies

        Note over SA,SS: Session Creation
        SA->>SS: createSession({username, email, userId})
        SS->>SA: Return JWT token
        SA->>SS: setSessionCookie(token)
        SA->>SF: Return {success: true, redirectTo: '/'}
        SF->>R: router.push('/')
        R->>MW: Request protected route
        MW->>MW: Check session cookie
        MW->>R: Allow access
        R->>U: Render dashboard
    else Authentication Failed
        SA->>SF: Return {success: false, error: message}
        SF->>U: Display error message
    else Requires Verification
        SA->>SF: Return {requiresVerification: true}
        SF->>U: Show verification link
    end
```

### 2. Sign-Up Flow

```mermaid
sequenceDiagram
    participant U as User
    participant SUF as SignUpForm
    participant SA as registerUser Action
    participant AMP as AWS Amplify
    participant COG as AWS Cognito
    participant EMAIL as Email Service

    U->>SUF: Enter name, email, password
    U->>SUF: Submit form
    SUF->>SA: formAction(formData + csrfToken)
    SA->>SA: Validate CSRF token
    SA->>SA: Validate form data
    SA->>SA: Check password confirmation
    SA->>AMP: signUp({username, password, attributes})
    AMP->>COG: Create user account
    COG->>EMAIL: Send verification email
    COG->>AMP: Return {nextStep: 'CONFIRM_SIGN_UP'}
    AMP->>SA: Return signup result
    SA->>SUF: Return {success: true, nextStep: 'verify', email}
    SUF->>U: Redirect to verification page
```

### 3. Email Verification Flow

```mermaid
sequenceDiagram
    participant U as User
    participant VF as VerifyForm
    participant SA as verifyEmail Action
    participant AMP as AWS Amplify
    participant COG as AWS Cognito

    U->>VF: Enter email & verification code
    U->>VF: Submit form
    VF->>SA: formAction(formData + csrfToken)
    SA->>SA: Validate CSRF token
    SA->>SA: Validate email & code
    SA->>AMP: confirmSignUp({username, confirmationCode})
    AMP->>COG: Verify confirmation code
    COG->>AMP: Return verification result
    AMP->>SA: Return success
    SA->>VF: Return {success: true}
    VF->>U: Redirect to sign-in page
```

### 4. Password Reset Flow

```mermaid
sequenceDiagram
    participant U as User
    participant FPF as ForgotPasswordForm
    participant RPF as ResetPasswordForm
    participant SA1 as initiatePasswordReset Action
    participant SA2 as completePasswordReset Action
    participant AMP as AWS Amplify
    participant COG as AWS Cognito
    participant EMAIL as Email Service
    participant SS as SessionStorage

    Note over U,SS: Step 1: Initiate Password Reset
    U->>FPF: Navigate to /forgot-password
    U->>FPF: Enter email address
    U->>FPF: Submit form
    FPF->>SA1: formAction(formData + csrfToken)
    SA1->>SA1: Validate CSRF token
    SA1->>SA1: Validate email format
    SA1->>AMP: resetPassword({username: email})
    AMP->>COG: Request password reset
    COG->>EMAIL: Send 6-digit reset code
    COG->>AMP: Return reset initiated
    AMP->>SA1: Return success
    SA1->>FPF: Return {success: true, nextStep: 'reset'}
    FPF->>SS: Store email in sessionStorage
    FPF->>U: Redirect to /reset-password

    Note over U,SS: Step 2: Complete Password Reset
    U->>RPF: Navigate to /reset-password
    RPF->>SS: Retrieve stored email
    RPF->>U: Pre-fill email field
    U->>RPF: Enter 6-digit code
    U->>RPF: Enter new password
    U->>RPF: Confirm new password
    U->>RPF: Submit form
    RPF->>SA2: formAction(formData + csrfToken)
    SA2->>SA2: Validate CSRF token
    SA2->>SA2: Validate all fields
    SA2->>SA2: Check password confirmation
    SA2->>SA2: Validate password strength
    SA2->>AMP: confirmResetPassword({username, code, newPassword})
    AMP->>COG: Confirm password reset
    COG->>AMP: Return success
    AMP->>SA2: Return success
    SA2->>RPF: Return {success: true}
    RPF->>SS: Clear stored email
    RPF->>U: Redirect to /signin with success message
```

### 5. Protected Route Access

```mermaid
sequenceDiagram
    participant U as User
    participant MW as Middleware
    participant SS as Session Service
    participant P as Protected Page

    U->>MW: Request /protected-route
    MW->>MW: Check session cookie exists

    alt Session Cookie Present
        MW->>SS: verifySession(sessionToken)
        SS->>SS: Verify JWT signature & expiry
        SS->>MW: Return session data
        MW->>P: Allow access
        P->>U: Render protected content
    else No Session Cookie
        MW->>U: Redirect to /auth/signin?from=/protected-route
    else Invalid Session
        MW->>SS: clearSession()
        MW->>U: Redirect to /auth/signin
    end
```

### 6. Logout Flow

```mermaid
sequenceDiagram
    participant U as User
    participant LB as LogoutButton
    participant SA as logout Action
    participant AMP as AWS Amplify
    participant COG as AWS Cognito
    participant SS as Session Service

    U->>LB: Click logout button
    LB->>SA: logout()
    SA->>SA: ensureAmplifyConfig()
    SA->>AMP: signOut()
    AMP->>COG: Invalidate Cognito session
    SA->>SS: clearSession()
    SS->>SS: Delete session & CSRF cookies
    SA->>U: redirect('/auth/signin')
```

## Detailed Function Analysis

### Core Authentication Functions

#### 1. `ServerSideTokenStorage` Class

**Location**: `app/auth/actions.ts:30-55`

**Purpose**: Custom token storage implementation for server-side Amplify operations

**Methods**:

```typescript
class ServerSideTokenStorage {
  private tokens: Map<string, any> = new Map();

  async setItem(key: string, value: string): Promise<void>;
  async getItem(key: string): Promise<string | null>;
  async removeItem(key: string): Promise<void>;
  async clear(): Promise<void>;
}
```

**Key Features**:

- In-memory storage prevents SSR token persistence issues
- Detailed logging for debugging token operations
- Temporary storage during authentication flow
- Implements Amplify's storage interface

**Usage**: Configured in `ensureAmplifyConfig()` to replace default localStorage

#### 2. `ensureAmplifyConfig(): void`

**Location**: `app/auth/actions.ts:65-95`

**Purpose**: Configures Amplify for server-side operations with custom token storage

**Implementation**:

```typescript
const config = {
  Auth: {
    Cognito: {
      userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID!,
      userPoolClientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID!,
      signUpVerificationMethod: 'code',
      loginWith: { email: true, username: false, phone: false },
    },
  },
  Storage: serverTokenStorage, // Custom storage implementation
};
```

**Features**:

- Prevents "Amplify has not been configured" errors
- Uses custom token storage for server operations
- Fallback configuration handling
- Environment-based configuration

#### 3. `authenticateUser(prevState, formData): Promise<AuthResult>`

**Location**: `app/auth/actions.ts:96`

**Purpose**: Handles user sign-in with email and password

**Detailed Flow**:

1. **Configuration**: Calls `ensureAmplifyConfig()` to configure AWS Amplify with custom token storage
2. **CSRF Validation**: Validates CSRF token using `validateCSRFToken(formData)`
3. **Input Validation**: Checks email and password are provided
4. **AWS Authentication**: Calls `signIn({username: email, password})`
5. **Token Management**: On success, retrieves user details and auth session tokens
   - Calls `getCurrentUser()` to get user details and userId
   - Calls `fetchAuthSession()` to get Cognito tokens (access, id)
   - Stores tokens in `ServerSideTokenStorage` for server operations
   - Stores access and ID tokens in secure HTTP-only cookies via `setCognitoTokenCookies()`
6. **Session Creation**: Creates JWT session with `createSession({username, email, userId})`
7. **Cookie Setting**: Sets secure HTTP-only cookie with `setSessionCookie()`
8. **Error Handling**: Maps Cognito errors to user-friendly messages with recovery logic

**Key Security Features**:

- CSRF token validation
- Generic error messages to prevent email enumeration
- Secure session creation with JWT
- HTTP-only cookie storage

**Error Mapping**:

```typescript
switch (error.name) {
  case 'UserNotConfirmedException':
    return { success: false, requiresVerification: true, error: 'Invalid email or password' };
  case 'NotAuthorizedException':
    return { success: false, error: 'Invalid email or password' };
  case 'UserNotFoundException':
    return { success: false, error: 'Invalid email or password' };
  case 'TooManyRequestsException':
    return { success: false, error: 'Too many attempts. Please try again later.' };
}
```

#### 2. `registerUser(prevState, formData): Promise<AuthResult>`

**Location**: `app/auth/actions.ts:204`

**Purpose**: Creates new user accounts with email verification

**Detailed Flow**:

1. **CSRF Validation**: Validates CSRF token
2. **Input Validation**: Validates name, email, password, and password confirmation
3. **Password Strength**: Checks minimum 8 character requirement
4. **AWS Registration**: Calls `signUp()` with user attributes
5. **Verification Setup**: Returns verification instructions for email confirmation

**Validation Steps**:

```typescript
// Required fields check
if (!name || !email || !password) {
  return { success: false, error: 'Name, email and password are required' };
}

// Password confirmation
if (password !== confirmPassword) {
  return { success: false, error: 'Passwords do not match' };
}

// Password strength
if (password.length < 8) {
  return { success: false, error: 'Password must be at least 8 characters long' };
}
```

#### 3. `verifyEmail(prevState, formData): Promise<AuthResult>`

**Location**: `app/auth/actions.ts:290`

**Purpose**: Confirms user email with verification code

**Detailed Flow**:

1. **CSRF Validation**: Validates CSRF token
2. **Input Validation**: Validates email and verification code
3. **AWS Confirmation**: Calls `confirmSignUp()` with code
4. **Error Handling**: Handles expired codes, invalid codes, etc.

**AWS Integration**:

```typescript
await confirmSignUp({
  username: email,
  confirmationCode: code,
});
```

#### 4. `initiatePasswordReset(prevState, formData): Promise<AuthResult>`

**Location**: `app/(auth)/actions.ts:463`

**Purpose**: Initiates password reset process by sending reset code to user's email

**Detailed Flow**:

1. **Configuration**: Calls `ensureAmplifyConfig()` to configure AWS Amplify
2. **CSRF Validation**: Validates CSRF token using `validateCSRFToken(formData)`
3. **Input Validation**: Checks email is provided and valid
4. **AWS Reset Request**: Calls `resetPassword({username: email})`
5. **Email Delivery**: Cognito sends 6-digit reset code to user's email
6. **Response**: Returns success with next step indicator

**Security Features**:

- CSRF token validation
- Generic error messages to prevent email enumeration
- 15-minute code expiration
- Rate limiting protection

**Error Mapping**:

```typescript
switch (error.name) {
  case 'UserNotFoundException':
    return { success: false, error: 'No account found with this email' };
  case 'NotAuthorizedException':
    return { success: false, error: 'User account is disabled' };
  default:
    return { success: false, error: 'Failed to send reset code. Please try again.' };
}
```

#### 5. `completePasswordReset(prevState, formData): Promise<AuthResult>`

**Location**: `app/(auth)/actions.ts:515`

**Purpose**: Completes password reset with verification code and new password

**Detailed Flow**:

1. **Configuration**: Calls `ensureAmplifyConfig()` to configure AWS Amplify
2. **CSRF Validation**: Validates CSRF token using `validateCSRFToken(formData)`
3. **Input Validation**: Validates email, code, and password fields
4. **Password Confirmation**: Ensures password and confirmPassword match
5. **Password Strength**: Validates minimum 8 character requirement
6. **AWS Reset Completion**: Calls `confirmResetPassword({username, confirmationCode, newPassword})`
7. **Success Response**: Returns success indicator for redirect

**Validation Steps**:

```typescript
// Required fields check
if (!email || !code || !newPassword) {
  return { success: false, error: 'All fields are required' };
}

// Password confirmation
if (newPassword !== confirmPassword) {
  return { success: false, error: 'Passwords do not match' };
}

// Password strength
if (newPassword.length < 8) {
  return { success: false, error: 'Password must be at least 8 characters long' };
}
```

**Error Mapping**:

```typescript
switch (error.name) {
  case 'CodeMismatchException':
    return { success: false, error: 'Invalid reset code' };
  case 'ExpiredCodeException':
    return { success: false, error: 'Reset code has expired' };
  case 'InvalidPasswordException':
    return { success: false, error: 'Password does not meet requirements' };
  default:
    return { success: false, error: 'Password reset failed. Please try again.' };
}
```

#### 6. `logout(): Promise<void>`

**Location**: `app/auth/actions.ts:469`

**Purpose**: Logs out user and cleans up session

**Process**:

1. **AWS Logout**: Calls `amplifySignOut()` to invalidate Cognito session
2. **Session Cleanup**: Calls `clearSession()` to remove cookies
3. **Redirect**: Redirects to sign-in page
4. **Error Handling**: Continues cleanup even if AWS logout fails

### Session Management Functions

#### 1. `createSession(userData): Promise<string>`

**Location**: `lib/session.ts:60`

**Purpose**: Creates signed JWT session token

**Implementation Details**:

```typescript
const payload: SessionPayload = {
  username: userData.username,
  email: userData.email,
  userId: userData.userId,
  signedIn: true,
  iat: Math.floor(now / 1000),
  exp: Math.floor(exp / 1000),
  jti: generateSecureToken(16), // Unique session ID
};

return await new SignJWT(payload as unknown as JWTPayload)
  .setProtectedHeader({ alg: 'HS256' })
  .setIssuedAt()
  .setExpirationTime(payload.exp)
  .setJti(jti)
  .sign(SESSION_SECRET);
```

**Features**:

- Uses HS256 algorithm for JWT signing
- 7-day expiration time
- Includes unique session ID (jti) for tracking
- Stores username, email, and authentication status

#### 2. `setSessionCookie(sessionToken): Promise<void>`

**Location**: `lib/session.ts:134`

**Purpose**: Sets secure session cookie

**Security Configuration**:

```typescript
cookieStore.set(SESSION_COOKIE_NAME, sessionToken, {
  httpOnly: true, // Prevents XSS access
  secure: process.env.NODE_ENV === 'production', // HTTPS only in production
  sameSite: 'strict', // CSRF protection
  maxAge: SESSION_DURATION / 1000, // 7 days
  path: '/', // Root path access
});
```

#### 3. `verifySession(token): Promise<SessionPayload | null>`

**Location**: `lib/session.ts:86`

**Purpose**: Verifies and decodes JWT session token

**Validation Steps**:

1. JWT signature verification using `jwtVerify()`
2. Expiration time check
3. Payload structure validation
4. Returns null for invalid tokens

```typescript
const { payload } = await jwtVerify(sessionToken, SESSION_SECRET);
return payload as SessionPayload;
```

#### 4. `clearSession(): Promise<void>`

**Location**: `lib/session.ts:188`

**Purpose**: Removes session and CSRF cookies

**Implementation**:

```typescript
const cookieStore = await cookies();
cookieStore.delete(SESSION_COOKIE_NAME);
cookieStore.delete(CSRF_COOKIE_NAME);
```

### CSRF Protection Functions

#### 1. `generateCSRFToken(): Promise<string>`

**Location**: `lib/session.ts:145`

**Purpose**: Generates secure CSRF tokens

**Implementation**:

```typescript
const token = generateSecureToken(); // 32-byte random token
const exp = Date.now() + 60 * 60 * 1000; // 1-hour expiration

const csrfData: CSRFToken = { token, exp };
cookieStore.set(CSRF_COOKIE_NAME, JSON.stringify(csrfData), {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict',
  maxAge: 60 * 60, // 1 hour
  path: '/',
});
```

**Features**:

- 32-byte random token generation
- 1-hour expiration time
- Stored in HTTP-only cookie
- JSON structure with token and expiry

#### 2. `verifyCSRFToken(providedToken): Promise<boolean>`

**Location**: `lib/session.ts:166`

**Purpose**: Validates CSRF tokens

**Validation Process**:

1. Retrieves CSRF cookie
2. Parses JSON structure
3. Compares provided token with stored token
4. Checks expiration time

```typescript
const csrfData: CSRFToken = JSON.parse(csrfCookie.value);
if (csrfData.token === providedToken && Date.now() < csrfData.exp) {
  return true;
}
```

### Middleware Functions

#### 1. `middleware(request): Promise<NextResponse>`

**Location**: `middleware.ts:20`

**Purpose**: Comprehensive request processing

**Responsibilities**:

1. **Security Headers**: Adds CSP, XSS protection, etc.
2. **Route Protection**: Checks authentication for protected routes
3. **Public Route Handling**: Redirects authenticated users from auth pages
4. **Rate Limiting**: Prevents brute force attacks (production only)

**Protected Routes**: `/devices`, `/earnings`, `/edit-device`, `/profile`, `/team`
**Public Routes**: `/auth/signin`, `/auth/signup`, `/auth/verify`, `/forgot-password`, `/reset-password`

**Session Check Logic**:

```typescript
const sessionCookieName = process.env.NODE_ENV === 'production' ? '__Secure-session' : 'session';
const sessionCookie =
  request.cookies.get(sessionCookieName) || request.cookies.get('amplify-auth-token');
const isAuthenticated = !!sessionCookie;
```

**Security Headers Applied**:

- `X-Frame-Options: DENY` - Prevents clickjacking
- `X-Content-Type-Options: nosniff` - Prevents MIME sniffing
- `X-XSS-Protection: 1; mode=block` - XSS protection
- `Content-Security-Policy` - Comprehensive CSP
- `Strict-Transport-Security` - HTTPS enforcement (production)

## API Integration

### External API Authentication Strategy

The application uses a sophisticated approach to handle authentication for external APIs while maintaining security and separation of concerns.

#### 1. **QBraid API Integration**

**Location**: `api-calls/client.ts`

**Authentication Method**: Bearer token authorization with secure cookie retrieval

```typescript
// Request interceptor to add auth headers
client.interceptors.request.use(async (config: InternalAxiosRequestConfig) => {
  config.headers = config.headers || {};

  try {
    // Get tokens from secure cookies
    const tokens = await getCognitoTokenCookies();
    const userEmail = await getUserEmailFromToken();

    // Use access token for authorization
    if (tokens.accessToken) {
      config.headers['authorization'] = `Bearer ${tokens.accessToken}`;
    }

    // Use email from ID token
    if (userEmail) {
      config.headers['email'] = userEmail;
    }

    // Fallback to environment variables if no cookies
    if (!tokens.accessToken && !userEmail) {
      if (process.env.NEXT_PUBLIC_EMAIL) {
        config.headers['email'] = process.env.NEXT_PUBLIC_EMAIL;
      }
      if (process.env.NEXT_PUBLIC_REFRESH_TOKEN) {
        config.headers['refresh-token'] = process.env.NEXT_PUBLIC_REFRESH_TOKEN;
      }
    }
  } catch (error) {
    // Fallback to environment variables on error
  }

  return config;
});
```

**Features**:

- Automatic Bearer token authorization from secure cookies
- Email extraction from ID token payload
- Environment variable fallback for development
- Centralized error handling and logging
- No client-side token exposure

#### 2. **API Client Configuration**

**Base URL Strategy**:

```typescript
const baseURL =
  env === 'production'
    ? 'https://api.qbraid.com/api'
    : env === 'staging'
      ? 'https://api-staging-1.qbraid.com/api'
      : process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';
```

**Security Features**:

- Environment-specific endpoints
- Secure HTTP-only cookie token storage
- Automatic error handling and logging
- Request/response interceptors for monitoring
- Bearer token authorization
- No client-side token exposure

#### 3. **API Endpoints Coverage**

The application provides comprehensive API coverage for:

- **Quantum Devices**: `/quantum-devices` - Device management and data
- **Quantum Jobs**: `/quantum-jobs` - Job execution and monitoring
- **Organizations**: `/orgs` - Organization and user management
- **Audit Logs**: `/audit-logs` - Activity tracking and compliance
- **User Management**: `/orgs/users` - Team and permission management

#### 4. **Current Token Management**

**Clean Implementation**: Secure server-side token storage

```typescript
// Current implementation - clean and secure
export async function setCognitoTokenCookies(tokens: {
  accessToken?: string;
  idToken?: string;
}): Promise<void> {
  const cookieStore = await cookies();
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: 60 * 60, // 1 hour
    path: '/',
  };

  if (tokens.accessToken) {
    cookieStore.set(ACCESS_TOKEN_COOKIE_NAME, tokens.accessToken, cookieOptions);
  }

  if (tokens.idToken) {
    cookieStore.set(ID_TOKEN_COOKIE_NAME, tokens.idToken, cookieOptions);
  }
}
```

**Benefits**:

- No hacky refresh token handling
- Secure HTTP-only cookies
- Automatic token expiration
- Clean separation of concerns
- Amplify manages token refresh internally

## Component Breakdown

### 1. SignInForm Component

**Location**: `components/auth/sign-in-form.tsx`

**Key Features**:

- Uses `useActionState` for form state management
- Integrates CSRF protection with `useCSRFToken` hook
- Handles redirect parameters for post-auth navigation
- Shows verification link for unverified users

**State Management**:

```typescript
const [state, formAction] = useActionState(authenticateUser, initialState);
const { csrfToken, loading: csrfLoading, error: csrfError } = useCSRFToken();
```

**Form Structure**:

```typescript
<form action={formAction} className="space-y-4">
  <CSRFTokenInput csrfToken={csrfToken} />
  {/* Email and password inputs */}
  <Button type="submit" disabled={csrfLoading || !csrfToken}>
    {csrfLoading ? 'Loading...' : 'Sign in'}
  </Button>
</form>
```

**Redirect Handling**:

```typescript
useEffect(() => {
  if (state.success) {
    const redirectPath = state.redirectTo || redirect;
    router.push(redirectPath);
    router.refresh();
  }
}, [state.success, state.redirectTo, router, redirect]);
```

### 2. useCSRFToken Hook

**Location**: `lib/csrf.tsx:8`

**Purpose**: Fetches and manages CSRF tokens

**Implementation**:

```typescript
export function useCSRFToken() {
  const [csrfToken, setCSRFToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCSRFToken() {
      const response = await fetch('/api/csrf-token', {
        method: 'GET',
        credentials: 'same-origin',
      });
      const data = await response.json();
      setCSRFToken(data.csrfToken);
    }
    fetchCSRFToken();
  }, []);

  return { csrfToken, loading, error };
}
```

**Features**:

1. Fetches token from `/api/csrf-token` endpoint
2. Manages loading and error states
3. Provides token to form components
4. Handles cleanup on component unmount

### 3. CSRFTokenInput Component

**Location**: `lib/csrf.tsx:62`

**Purpose**: Hidden input field for CSRF tokens

**Implementation**:

```typescript
export function CSRFTokenInput({ csrfToken }: CSRFTokenInputProps) {
  if (!csrfToken) {
    return null;
  }
  return <input type="hidden" name="csrfToken" value={csrfToken} required />;
}
```

**Usage**: Automatically included in all authentication forms

### 4. ForgotPasswordForm Component

**Location**: `components/auth/forgot-password-form.tsx`

**Purpose**: Handles password reset initiation with email input

**Key Features**:

- Uses React Query `useMutation` for form state management
- Integrates CSRF protection with `useCSRFToken` hook
- Stores email securely in sessionStorage for next step
- Real-time email validation with visual feedback
- Responsive UI with loading states and success messages

**State Management**:

```typescript
const resetMutation = useMutation({
  mutationFn: async (formData: { email: string; csrfToken: string }) => {
    const formDataObj = new FormData();
    formDataObj.set('email', formData.email);
    formDataObj.set('csrfToken', formData.csrfToken);

    const result = await initiatePasswordReset(null, formDataObj);
    if (!result.success) {
      throw new Error(result.error || 'Failed to send reset code');
    }
    return result;
  },
  onSuccess: (data) => {
    if (data.nextStep === 'reset') {
      sessionStorage.setItem('reset-email', formState.email);
      router.push('/reset-password');
    }
  },
});
```

**Security Features**:

- CSRF token validation
- Email format validation
- Secure sessionStorage for email transfer
- No sensitive data in URL parameters
- Generic success messages

### 5. ResetPasswordForm Component

**Location**: `components/auth/reset-password-form.tsx`

**Purpose**: Handles password reset completion with code and new password

**Key Features**:

- Uses React Query `useMutation` for form state management
- Integrates CSRF protection with `useCSRFToken` hook
- Retrieves email from sessionStorage (pre-filled)
- 6-digit OTP input component for reset code
- Real-time password validation with strength indicators
- Password confirmation validation
- Show/hide password toggles

**State Management**:

```typescript
const [formState, setFormState] = useState<FormState>(() => {
  if (typeof window !== 'undefined') {
    const storedEmail = sessionStorage.getItem('reset-email');
    return {
      ...INITIAL_FORM_STATE,
      email: storedEmail || '',
      isEmailEditable: !storedEmail,
      initialized: true,
    };
  }
  return INITIAL_FORM_STATE;
});
```

**Password Validation**:

```typescript
interface PasswordValidation {
  minLength: boolean;
  hasNumber: boolean;
  hasSpecial: boolean;
}

const validatePassword = (password: string): PasswordValidation => ({
  minLength: password.length >= 8,
  hasNumber: /\d/.test(password),
  hasSpecial: /[!@#$%^&*(),.?":{}|<>]/.test(password),
});
```

**Form Validation**:

- Email format validation
- 6-digit code requirement
- Password strength validation (length, numbers, special characters)
- Password confirmation matching
- All fields required before submission

**Success Flow**:

```typescript
onSuccess: () => {
  // Clear stored email from sessionStorage
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('reset-email');
  }
  // Redirect with success message
  router.push('/signin?message=Password reset successful. Please sign in with your new password.');
},
```

### 6. LogoutButton Component

**Location**: `components/auth/logout-button.tsx`

**Purpose**: Handles user logout with customizable styling

**Implementation**:

```typescript
export function LogoutButton({ variant = 'outline', size = 'default', className, children = 'Sign out' }) {
  const handleLogout = async () => {
    await logout();
  };

  return (
    <Button variant={variant} size={size} className={className} onClick={handleLogout}>
      {children}
    </Button>
  );
}
```

**Features**:

- Customizable button styling
- Calls server action directly
- Handles async logout process

## Password Reset Pages and Routing

### 1. Forgot Password Page

**Location**: `app/(auth)/forgot-password/page.tsx`

**Purpose**: Entry point for password reset flow

**Implementation**:

```typescript
export default function ForgotPasswordPage() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Suspense fallback={<LoadingFallback />}>
          <ForgotPasswordForm />
        </Suspense>
      </div>
    </div>
  );
}
```

**Features**:

- Responsive layout with centered form
- Suspense boundary for loading states
- Consistent styling with other auth pages

### 2. Reset Password Page

**Location**: `app/(auth)/reset-password/page.tsx`

**Purpose**: Completion point for password reset flow

**Implementation**:

```typescript
export default function ResetPasswordPage() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Suspense fallback={<LoadingFallback />}>
          <ResetPasswordForm />
        </Suspense>
      </div>
    </div>
  );
}
```

**Features**:

- Consistent layout with forgot password page
- Suspense boundary for client-side hydration
- Retrieves email from sessionStorage automatically

### 3. Password Reset Navigation Flow

**Flow Overview**:

1. **Entry Points**:
   - Sign-in page "Forgot Password?" link → `/forgot-password`
   - Direct navigation to `/forgot-password`

2. **Step 1 - Email Input** (`/forgot-password`):
   - User enters email address
   - Form validates email format
   - On success: Email stored in sessionStorage
   - Automatic redirect to `/reset-password`

3. **Step 2 - Reset Completion** (`/reset-password`):
   - Email pre-filled from sessionStorage
   - User enters 6-digit verification code
   - User sets new password with confirmation
   - On success: sessionStorage cleared
   - Automatic redirect to `/signin` with success message

4. **Exit Points**:
   - "Back to Sign In" links on both pages
   - "Create Account" links for new users
   - Successful completion → `/signin`

### 4. SessionStorage Security

**Implementation Details**:

```typescript
// Store email securely for next step
sessionStorage.setItem('reset-email', formState.email);

// Retrieve and clear on completion
const storedEmail = sessionStorage.getItem('reset-email');
sessionStorage.removeItem('reset-email');
```

**Security Benefits**:

- **No URL Parameters**: Email not exposed in browser history
- **Session-Scoped**: Data cleared on tab close
- **Automatic Cleanup**: Removed after successful reset
- **Client-Side Only**: Not transmitted to server unnecessarily

**Fallback Handling**:

- If no stored email: User can manually enter email
- Email field becomes editable if sessionStorage empty
- Form validation ensures email is always provided

## Security Implementation

### 1. CSRF Protection

**Implementation Strategy**:

- **Double-submit cookie pattern**: Token stored in HTTP-only cookie and submitted in form
- **Server-generated tokens**: Cryptographically secure random tokens
- **Expiration**: 1-hour token lifetime with automatic renewal
- **Validation**: Server-side verification on all state-changing operations

**Token Generation**:

```typescript
const token = generateSecureToken(CSRF_TOKEN_LENGTH); // 32 bytes
const exp = Date.now() + 60 * 60 * 1000; // 1 hour
```

**Protection Scope**:

- All authentication forms (sign-in, sign-up, verify, password reset)
- State-changing operations
- API endpoints that modify data

### 2. Session Security

**JWT Implementation**:

```typescript
interface SessionPayload {
  username: string;
  userId?: string;
  email: string;
  signedIn: boolean;
  iat: number; // Issued at timestamp
  exp: number; // Expiration timestamp
  jti: string; // Unique session ID
}
```

**Security Features**:

- **HS256 signing**: Symmetric key algorithm for JWT signing
- **Secure secret**: Environment-based secret key
- **7-day expiration**: Automatic session timeout
- **Unique session IDs**: JWT ID (jti) for session tracking and revocation

**Cookie Security**:

```typescript
const cookieOptions = {
  httpOnly: true, // Prevents XSS access
  secure: process.env.NODE_ENV === 'production', // HTTPS only in production
  sameSite: 'strict' as const, // CSRF protection
  maxAge: SESSION_DURATION / 1000, // 7 days
  path: '/', // Root path access
};
```

### 3. Input Validation

**Server-Side Validation**:

- **Email format**: Standard email regex validation
- **Password strength**: Minimum 8 characters, complexity requirements
- **CSRF tokens**: Cryptographic verification
- **SQL injection prevention**: Parameterized queries and ORM usage

**Client-Side Validation**:

- **Required fields**: HTML5 required attributes
- **Type validation**: Email input types
- **Real-time feedback**: Form validation states

### 4. Error Handling

**Security-Conscious Messaging**:

```typescript
// Generic messages prevent email enumeration
switch (error.name) {
  case 'UserNotFoundException':
  case 'NotAuthorizedException':
  case 'UserNotConfirmedException':
    return { success: false, error: 'Invalid email or password' };
}
```

**Features**:

- **Generic error messages**: Prevent information disclosure
- **Consistent response times**: Prevent timing attacks
- **Detailed logging**: Server-side error tracking
- **Rate limiting**: Brute force protection

## Session Management

### Session Lifecycle

1. **Creation**: JWT token generated on successful authentication
2. **Storage**: Secure HTTP-only cookie with 7-day expiration
3. **Validation**: Middleware checks on every protected route access
4. **Renewal**: Automatic renewal on activity (if implemented)
5. **Destruction**: Explicit logout or expiration

### Session Data Structure

```typescript
interface SessionPayload {
  username: string; // User's email (used as username)
  userId?: string; // Optional Cognito user ID
  email: string; // User's email address
  signedIn: boolean; // Authentication status
  iat: number; // Issued at timestamp (Unix)
  exp: number; // Expiration timestamp (Unix)
  jti: string; // Unique session identifier
}
```

### Session Validation Process

1. **Cookie Retrieval**: Extract session cookie from request
2. **JWT Verification**: Verify signature using secret key
3. **Expiration Check**: Ensure token hasn't expired
4. **Payload Validation**: Validate required fields
5. **Session Data Return**: Return validated session data or null

## Error Handling

### Error Categories

1. **Authentication Errors**:
   - Invalid credentials
   - Unverified users
   - Account lockouts
   - Password reset requirements

2. **Password Reset Errors**:
   - Invalid or expired reset codes
   - Password strength validation failures
   - Email not found in system
   - Rate limiting on reset requests
   - Code mismatch or tampering

3. **Validation Errors**:
   - Missing required fields
   - Invalid email formats
   - Password strength failures
   - CSRF token mismatches

4. **Security Errors**:
   - CSRF token failures
   - Rate limiting triggers
   - Session tampering
   - Unauthorized access attempts

5. **System Errors**:
   - AWS service failures
   - Configuration issues
   - Network connectivity problems
   - Database connection errors

### Error Mapping Strategy

**Cognito Error → User Message Mapping**:

```typescript
const errorMap = {
  // Authentication errors
  UserNotConfirmedException: 'Invalid email or password',
  NotAuthorizedException: 'Invalid email or password',
  UserNotFoundException: 'Invalid email or password',
  PasswordResetRequiredException: 'Invalid email or password',
  TooManyRequestsException: 'Too many attempts. Please try again later.',

  // Verification errors
  CodeMismatchException: 'Invalid verification code',
  ExpiredCodeException: 'Verification code has expired',

  // Registration errors
  UsernameExistsException: 'An account with this email already exists',
  InvalidPasswordException: 'Password does not meet requirements',

  // Password reset specific errors
  'UserNotFoundException (Reset)': 'No account found with this email',
  'NotAuthorizedException (Reset)': 'User account is disabled',
  'CodeMismatchException (Reset)': 'Invalid reset code',
  'ExpiredCodeException (Reset)': 'Reset code has expired',
  'InvalidPasswordException (Reset)': 'Password does not meet requirements',
};
```

**Password Reset Error Handling Strategy**:

```typescript
// initiatePasswordReset error mapping
switch (error.name) {
  case 'UserNotFoundException':
    return { success: false, error: 'No account found with this email' };
  case 'NotAuthorizedException':
    return { success: false, error: 'User account is disabled' };
  default:
    return { success: false, error: 'Failed to send reset code. Please try again.' };
}

// completePasswordReset error mapping
switch (error.name) {
  case 'CodeMismatchException':
    return { success: false, error: 'Invalid reset code' };
  case 'ExpiredCodeException':
    return { success: false, error: 'Reset code has expired' };
  case 'InvalidPasswordException':
    return { success: false, error: 'Password does not meet requirements' };
  default:
    return { success: false, error: 'Password reset failed. Please try again.' };
}
```

**Security Benefits**:

- Prevents email enumeration attacks
- Provides consistent user experience
- Maintains security while being user-friendly
- Enables proper error tracking and monitoring

**Password Reset Security Considerations**:

- **Rate Limiting**: Prevents brute force attacks on reset codes
- **Code Expiration**: 15-minute expiration window for reset codes
- **Single Use Codes**: Reset codes are invalidated after successful use
- **Email Verification**: Codes only sent to verified email addresses
- **CSRF Protection**: All reset requests require valid CSRF tokens
- **Generic Error Messages**: Prevents email enumeration during reset
- **Secure Transport**: All communications over HTTPS in production
- **Session Isolation**: Reset process doesn't interfere with existing sessions

### Logging and Monitoring

**Server-Side Logging**:

```typescript
console.error('Authentication error:', {
  error: error.name,
  message: error.message,
  timestamp: new Date().toISOString(),
  userAgent: request.headers['user-agent'],
  ip: request.ip,
});
```

**Monitoring Points**:

- Failed authentication attempts
- CSRF token validation failures
- Rate limiting triggers
- Session validation errors
- AWS service errors
- Password reset requests and completions
- Invalid reset code attempts
- Expired reset code usage
- Password reset rate limiting events

## Session Verification Deep Dive

### Session Verification Architecture

The session verification system is the cornerstone of our authentication architecture, providing secure, stateless authentication checks across all protected resources.

```mermaid
flowchart TD
    A[Incoming Request] --> B{Has Session Cookie?}
    B -->|No| C[Redirect to Sign-in]
    B -->|Yes| D[Extract JWT Token]
    D --> E[Verify JWT Signature]
    E -->|Invalid| F[Clear Invalid Session]
    F --> C
    E -->|Valid| G[Check Token Expiration]
    G -->|Expired| H[Clear Expired Session]
    H --> C
    G -->|Valid| I[Validate Payload Structure]
    I -->|Invalid| J[Clear Malformed Session]
    J --> C
    I -->|Valid| K[Extract Session Data]
    K --> L[Check Session ID Uniqueness]
    L -->|Duplicate/Revoked| M[Clear Compromised Session]
    M --> C
    L -->|Valid| N[Allow Request]
    N --> O[Attach User Context]
```

### Session Verification Implementation

#### 1. `verifySession(token): Promise<SessionPayload | null>`

**Location**: `lib/session.ts:86`

**Purpose**: Core session validation with comprehensive security checks

**Implementation Flow**:

```typescript
export async function verifySession(sessionToken: string): Promise<SessionPayload | null> {
  try {
    // Step 1: JWT Signature Verification
    const { payload } = await jwtVerify(sessionToken, SESSION_SECRET);

    // Step 2: Type Safety Validation
    const sessionPayload = payload as SessionPayload;

    // Step 3: Required Fields Validation
    if (!sessionPayload.username || !sessionPayload.email || !sessionPayload.jti) {
      console.warn('Session payload missing required fields');
      return null;
    }

    // Step 4: Expiration Double-Check (JWT lib handles this, but explicit check for logging)
    if (sessionPayload.exp && Date.now() / 1000 > sessionPayload.exp) {
      console.warn('Session expired:', sessionPayload.jti);
      return null;
    }

    // Step 5: Session ID Validation (future: check against revocation list)
    // TODO: Implement session revocation checking
    // if (await isSessionRevoked(sessionPayload.jti)) {
    //   return null;
    // }

    return sessionPayload;
  } catch (error) {
    // Log security events for monitoring
    console.error('Session verification failed:', {
      error: error.message,
      timestamp: new Date().toISOString(),
      tokenPreview: sessionToken.substring(0, 20) + '...',
    });
    return null;
  }
}
```

#### 2. Middleware Session Verification

**Location**: `middleware.ts:28`

**Purpose**: Request-level session validation with route protection

**Implementation Details**:

```typescript
// Session validation in middleware
const sessionCookieName = process.env.NODE_ENV === 'production' ? '__Secure-session' : 'session';
const sessionCookie =
  request.cookies.get(sessionCookieName) || request.cookies.get('amplify-auth-token');

// Multi-layer session checking
const isAuthenticated = !!sessionCookie;

// For protected routes, perform deep session validation
if (protectedRoutes.some((route) => pathname.startsWith(route))) {
  if (!isAuthenticated) {
    // Redirect with original path for post-auth navigation
    const signInUrl = new URL('/auth/signin', request.url);
    signInUrl.searchParams.set('from', pathname);
    return NextResponse.redirect(signInUrl);
  }

  // Optional: Deep session validation (performance consideration)
  // const sessionData = await verifySession(sessionCookie.value);
  // if (!sessionData) {
  //   return NextResponse.redirect(new URL('/auth/signin', request.url));
  // }
}
```

### Session Verification Reasons & Security Rationale

#### 1. **Stateless Authentication**

- **Reason**: Eliminates server-side session storage requirements
- **Benefit**: Horizontal scalability, reduced memory footprint
- **Implementation**: JWT tokens contain all necessary user context

#### 2. **Cryptographic Integrity**

- **Reason**: Prevent session tampering and forgery
- **Benefit**: Mathematically guaranteed session authenticity
- **Implementation**: HMAC-SHA256 signature verification

#### 3. **Automatic Expiration**

- **Reason**: Limit exposure window for compromised tokens
- **Benefit**: Reduces impact of token theft
- **Implementation**: JWT `exp` claim with 7-day lifetime

#### 4. **Session Uniqueness**

- **Reason**: Enable session revocation and concurrent session management
- **Benefit**: Granular session control and security incident response
- **Implementation**: Unique `jti` (JWT ID) for each session

#### 5. **Fail-Safe Security**

- **Reason**: Default to denial when verification fails
- **Benefit**: Prevents unauthorized access during system failures
- **Implementation**: Return `null` on any validation failure

### Session Verification Performance Considerations

#### 1. **Middleware vs. Component Verification**

```typescript
// Middleware: Fast cookie presence check
const isAuthenticated = !!sessionCookie;

// Component: Full JWT verification when needed
const sessionData = await getSession();
```

**Trade-offs**:

- **Middleware**: Fast, suitable for route protection
- **Component**: Comprehensive, suitable for user data access

#### 2. **Caching Strategy** (Future Enhancement)

```typescript
// Redis-based session cache
const CACHE_TTL = 300; // 5 minutes

async function getCachedSession(jti: string): Promise<SessionPayload | null> {
  const cached = await redis.get(`session:${jti}`);
  return cached ? JSON.parse(cached) : null;
}

async function setCachedSession(jti: string, payload: SessionPayload): Promise<void> {
  await redis.setex(`session:${jti}`, CACHE_TTL, JSON.stringify(payload));
}
```

## Redis Configuration & Security

### Production-Ready Redis Setup

The application uses Redis for scalable token storage with enterprise-grade security and performance optimizations.

#### 1. **Environment Variables**

**Required Configuration:**

```bash
# Primary Redis connection (server-side only)
REDIS_URL=redis://localhost:6379

# Authentication (Redis 6+ ACL support)
REDIS_USERNAME=your_redis_username
REDIS_PASSWORD=your_secure_redis_password

# Legacy fallback (will be deprecated)
NEXT_PUBLIC_REDIS_URL=redis://localhost:6379
```

**Security Best Practices:**

- ✅ Use `REDIS_URL` instead of `NEXT_PUBLIC_REDIS_URL` to prevent client-side exposure
- ✅ Enable Redis authentication with strong passwords
- ✅ Use Redis 6+ ACL for fine-grained access control
- ✅ Enable TLS encryption for production environments
- ✅ Configure proper firewall rules and network security

#### 2. **Redis Client Configuration**

**Production Optimizations:**

```typescript
const redisConfig: RedisOptions = {
  // Connection settings with production-ready timeouts
  connectTimeout: 10000, // 10 seconds for initial connection
  commandTimeout: 5000, // 5 seconds for commands
  lazyConnect: true, // Only connect when needed

  // Retry strategy for production resilience
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  retryDelayOnClusterDown: 300,

  // Connection pool settings
  family: 4, // Use IPv4
  keepAlive: 30000, // 30 seconds

  // Security settings
  tls: process.env.NODE_ENV === 'production' ? {} : undefined,
  password: process.env.REDIS_PASSWORD,
  username: process.env.REDIS_USERNAME,
};
```

#### 3. **Token Storage Security**

**Key Features:**

- **Automatic TTL**: Tokens expire automatically (1 hour default)
- **Secure Key Prefixing**: `auth:tokens:` namespace isolation
- **Graceful Fallback**: In-memory storage if Redis unavailable
- **Connection Monitoring**: Health checks and performance tracking
- **Error Recovery**: Automatic reconnection and error handling

**Storage Implementation:**

```typescript
// Secure token storage with TTL
await tokenStorage.setItem(key, token, 3600); // 1 hour TTL

// Automatic cleanup and expiration
const token = await tokenStorage.getItem(key); // null if expired

// Bulk operations for cleanup
await tokenStorage.clear(); // Development only
```

#### 4. **Health Monitoring**

**Health Check Endpoint:**

```bash
curl http://localhost:3000/api/health/redis
```

**Response Example:**

```json
{
  "timestamp": "2025-07-06T23:07:16.381Z",
  "redis": {
    "available": true,
    "healthy": true,
    "status": "ready",
    "latency": 19,
    "operations": {
      "ping": 2,
      "set": 3,
      "get": 1,
      "del": 2
    },
    "serverInfo": {
      "version": "7.0.15",
      "mode": "standalone",
      "role": "master"
    },
    "stats": {
      "memory": {
        "used": "1.2M",
        "peak": "1.5M",
        "fragmentation": 1.1
      },
      "keys": {
        "total": 42,
        "expires": 38
      },
      "performance": {
        "hitRate": 98.5
      }
    }
  },
  "environment": {
    "redisUrl": "configured",
    "nodeEnv": "production",
    "hasAuth": true,
    "tlsEnabled": true
  }
}
```

#### 5. **Production Deployment**

**Docker Compose Example:**

```yaml
version: '3.8'
services:
  app:
    environment:
      - REDIS_URL=redis://redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_USERNAME=${REDIS_USERNAME}
    depends_on:
      - redis

  redis:
    image: redis:7-alpine
    command: >
      redis-server
      --requirepass ${REDIS_PASSWORD}
      --appendonly yes
      --appendfsync everysec
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - '6379:6379'
    restart: unless-stopped

volumes:
  redis_data:
```

**Kubernetes Example:**

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: redis:7-alpine
          command:
            - redis-server
            - --requirepass
            - $(REDIS_PASSWORD)
            - --appendonly
            - yes
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: redis-secret
                  key: password
          ports:
            - containerPort: 6379
          volumeMounts:
            - name: redis-storage
              mountPath: /data
      volumes:
        - name: redis-storage
          persistentVolumeClaim:
            claimName: redis-pvc
```

#### 6. **Serverless Configuration**

**Environment Variables for Serverless:**

```bash
# Enable Redis-based session storage (default for serverless)
SESSION_STORAGE_MODE=redis

# Redis connection (required for serverless)
REDIS_URL=redis://your-redis-host:6379
REDIS_PASSWORD=your_secure_password

# Session configuration
SESSION_SECRET=your-super-secure-session-secret-32-chars-min
```

**Serverless Platform Examples:**

**Vercel:**

```bash
# vercel.json
{
  "env": {
    "SESSION_STORAGE_MODE": "redis",
    "REDIS_URL": "@redis-url",
    "REDIS_PASSWORD": "@redis-password",
    "SESSION_SECRET": "@session-secret"
  }
}
```

**AWS Lambda:**

```yaml
# serverless.yml
environment:
  SESSION_STORAGE_MODE: redis
  REDIS_URL: ${env:REDIS_URL}
  REDIS_PASSWORD: ${env:REDIS_PASSWORD}
  SESSION_SECRET: ${env:SESSION_SECRET}
```

**Testing Serverless Session Storage:**

```bash
# Test Redis-based session storage
curl http://localhost:3000/api/debug/session-storage

# Expected response for working Redis:
{
  "summary": {
    "passed": 5,
    "total": 5,
    "success": true,
    "recommendation": "Redis-based session storage is working correctly"
  }
}
```

#### 7. **Security Checklist**

**✅ Configuration Security:**

- [ ] Use `REDIS_URL` instead of `NEXT_PUBLIC_REDIS_URL`
- [ ] Enable Redis authentication (`requirepass`)
- [ ] Configure Redis ACL users with minimal permissions
- [ ] Enable TLS encryption for production
- [ ] Set proper Redis configuration (`maxmemory`, `timeout`)
- [ ] Configure firewall rules (Redis port 6379)
- [ ] Use Redis Sentinel or Cluster for high availability

**✅ Serverless Security:**

- [ ] Set `SESSION_STORAGE_MODE=redis` for serverless environments
- [ ] Use Redis for session storage instead of cookies
- [ ] Implement session ID-based token retrieval
- [ ] Configure proper Redis TTL for sessions (7 days) and tokens (1 hour)
- [ ] Test session storage with `/api/debug/session-storage`
- [ ] Ensure Redis connection pooling for function reuse
- [ ] Monitor Redis connection limits for concurrent functions

**✅ Application Security:**

- [ ] Implement proper error handling and logging
- [ ] Use secure key prefixing (`session:`, `cognito:`, `auth:tokens:`)
- [ ] Set appropriate TTL for all keys
- [ ] Monitor Redis performance and memory usage
- [ ] Implement graceful shutdown handlers
- [ ] Use connection pooling and retry strategies
- [ ] Regular security audits and updates

**✅ Monitoring & Alerting:**

- [ ] Set up Redis monitoring (memory, connections, latency)
- [ ] Configure alerts for Redis downtime
- [ ] Monitor token storage statistics
- [ ] Track authentication performance metrics
- [ ] Log security events and anomalies

## Debug and Testing

### Debug Endpoints

The application provides several debug endpoints for testing and monitoring authentication functionality.

#### 1. **Auth Configuration Test**

**Endpoint**: `GET /api/debug/auth-test`

**Location**: `app/api/debug/auth-test/route.ts`

**Purpose**: Validates Amplify configuration and environment setup

**Response**:

```json
{
  "success": true,
  "amplifyConfig": {
    "hasAuth": true,
    "hasCognito": true,
    "userPoolId": "us-east-1_...",
    "clientId": "abc123...",
    "hasStorage": true
  },
  "environment": {
    "userPoolId": true,
    "clientId": true,
    "userPoolIdValue": "us-east-1_...",
    "clientIdValue": "abc123..."
  },
  "duration": 45
}
```

**Features**:

- Validates Amplify configuration
- Checks environment variables
- Measures response time
- Provides detailed configuration status

#### 2. **CSRF Token Endpoint**

**Endpoint**: `GET /api/csrf-token`

**Location**: `app/api/csrf-token/route.ts`

**Purpose**: Generates CSRF tokens for client-side forms

**Security Features**:

- HTTP-only cookie storage
- 1-hour expiration
- Cache-control headers to prevent caching
- Method restrictions (GET only)

#### 3. **Token Debug Endpoint**

**Endpoint**: `GET /api/debug/tokens`

**Location**: `app/api/debug/tokens/route.ts`

**Purpose**: Debug endpoint to check stored Cognito tokens

**Response**:

```json
{
  "success": true,
  "tokens": {
    "hasAccessToken": true,
    "hasIdToken": true,
    "accessTokenPreview": "eyJhbGciOiJSUzI1NiIs...",
    "idTokenPreview": "eyJhbGciOiJSUzI1NiIs..."
  },
  "userEmail": "<EMAIL>",
  "duration": 25
}
```

**Features**:

- Validates token cookie retrieval
- Shows token availability status
- Extracts user email from ID token
- Provides token previews (first 20 characters)
- Measures response time

#### 4. **Token Storage Endpoint**

**Endpoint**: `POST /api/store-cognito-tokens`

**Location**: `app/api/store-cognito-tokens/route.ts`

**Purpose**: Manually store Cognito tokens in secure cookies

**Request Body**:

```json
{
  "accessToken": "eyJhbGciOiJSUzI1NiIs...",
  "idToken": "eyJhbGciOiJSUzI1NiIs..."
}
```

**Features**:

- Manual token storage for testing
- Secure HTTP-only cookie configuration
- Validation of token presence
- Status reporting

#### 5. **ServerSideTokenStorage Implementation**

**Location**: `app/auth/actions.ts:30-55`

**Purpose**: Custom token storage for server-side Amplify operations

```typescript
class ServerSideTokenStorage {
  private tokens: Map<string, any> = new Map();

  async setItem(key: string, value: string): Promise<void> {
    console.log(`🔐 [TOKEN-STORAGE] Setting token: ${key}`);
    this.tokens.set(key, value);
  }

  async getItem(key: string): Promise<string | null> {
    const value = this.tokens.get(key) || null;
    console.log(`🔐 [TOKEN-STORAGE] Getting token: ${key} - ${value ? 'found' : 'not found'}`);
    return value;
  }
}
```

**Features**:

- In-memory token storage for server operations
- Detailed logging for debugging
- Prevents SSR token persistence issues
- Temporary storage during authentication flow

### Testing Strategy

#### 1. **Unit Tests**

The application includes comprehensive unit tests for API functions:

- **Device API Tests**: `api-calls/devices.test.ts`
- **Jobs API Tests**: `api-calls/jobs.test.ts`
- **User API Tests**: `api-calls/users.test.ts`
- **Logs API Tests**: `api-calls/logs.test.ts`

#### 2. **Integration Testing**

**Mock Implementation**: Uses Vitest for mocking API calls

```typescript
describe('devices API', () => {
  it('fetchAllDevices calls correct endpoint and returns data', async () => {
    const mockData = [{ qbraid_id: '1', name: 'Device 1' }];
    vi.spyOn(client, 'get').mockResolvedValue({ data: mockData });
    const result = await fetchAllDevices();
    expect(client.get).toHaveBeenCalledWith('/quantum-devices');
    expect(result).toEqual(mockData);
  });
});
```

#### 3. **Development Tools**

- **React Query Devtools**: Available in development mode
- **Detailed Console Logging**: Comprehensive auth flow logging
- **Error Boundary**: Global error handling and reporting

## Future Architecture Enhancements

### 1. Token Management Improvements

#### Current Clean Implementation

**Secure Server-Side Token Storage**: The current implementation provides a clean, secure approach:

```typescript
// Clean token storage without hacky workarounds
export async function setCognitoTokenCookies(tokens: {
  accessToken?: string;
  idToken?: string;
}): Promise<void> {
  const cookieStore = await cookies();
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: 60 * 60, // 1 hour
    path: '/',
  };

  if (tokens.accessToken) {
    cookieStore.set(ACCESS_TOKEN_COOKIE_NAME, tokens.accessToken, cookieOptions);
  }

  if (tokens.idToken) {
    cookieStore.set(ID_TOKEN_COOKIE_NAME, tokens.idToken, cookieOptions);
  }
}
```

**Benefits of Current Approach**:

- ✅ **No hacky refresh token handling**
- ✅ **Secure HTTP-only cookies**
- ✅ **Automatic token expiration**
- ✅ **Clean separation of concerns**
- ✅ **Amplify manages token refresh internally**
- ✅ **Bearer token authorization**
- ✅ **Email extraction from ID token**

#### Future Enhancements

**Potential Improvements** (without compromising security):

```typescript
// Enhanced token validation and refresh handling
export async function validateTokensAndRefresh(): Promise<boolean> {
  try {
    const tokens = await getCognitoTokenCookies();

    if (!tokens.accessToken) {
      // Trigger Amplify's internal token refresh
      const session = await fetchAuthSession({ forceRefresh: true });

      if (session.tokens) {
        await setCognitoTokenCookies({
          accessToken: session.tokens.accessToken?.toString(),
          idToken: session.tokens.idToken?.toString(),
        });
        return true;
      }
    }

    return !!tokens.accessToken;
  } catch (error) {
    console.error('Token validation failed:', error);
    return false;
  }
}
```

### 2. OAuth 2.0 / OpenID Connect Integration

#### Architecture Overview

```mermaid
graph TB
    subgraph "Current Architecture"
        A[Cognito Direct Auth]
        B[JWT Sessions]
        C[CSRF Protection]
    end

    subgraph "OAuth 2.0 Enhancement"
        D[OAuth Provider] --> E[Authorization Server]
        E --> F[Token Endpoint]
        F --> G[UserInfo Endpoint]
        H[PKCE Flow]
        I[State Parameter]
        J[Nonce Validation]
    end

    subgraph "Enhanced Session Management"
        K[Access Tokens]
        L[Refresh Tokens]
        M[ID Tokens]
        N[Token Refresh Logic]
    end

    A --> D
    B --> K
    C --> I
```

#### Implementation Strategy

**1. OAuth Provider Configuration**

```typescript
// lib/oauth-config.ts
interface OAuthProvider {
  name: string;
  clientId: string;
  clientSecret: string;
  authorizationUrl: string;
  tokenUrl: string;
  userInfoUrl: string;
  scopes: string[];
  pkceRequired: boolean;
}

const oauthProviders: Record<string, OAuthProvider> = {
  google: {
    name: 'Google',
    clientId: process.env.GOOGLE_CLIENT_ID!,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
    tokenUrl: 'https://oauth2.googleapis.com/token',
    userInfoUrl: 'https://www.googleapis.com/oauth2/v2/userinfo',
    scopes: ['openid', 'email', 'profile'],
    pkceRequired: true,
  },
  microsoft: {
    name: 'Microsoft',
    clientId: process.env.MICROSOFT_CLIENT_ID!,
    clientSecret: process.env.MICROSOFT_CLIENT_SECRET!,
    authorizationUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
    tokenUrl: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
    userInfoUrl: 'https://graph.microsoft.com/v1.0/me',
    scopes: ['openid', 'email', 'profile'],
    pkceRequired: true,
  },
};
```

**2. Enhanced Session Payload**

```typescript
interface EnhancedSessionPayload extends SessionPayload {
  // OAuth-specific fields
  provider?: 'cognito' | 'google' | 'microsoft' | 'github';
  accessToken?: string;
  refreshToken?: string;
  idToken?: string;
  tokenExpiry?: number;

  // Enhanced user context
  roles?: string[];
  permissions?: string[];
  organizationId?: string;
  lastActivity?: number;
}
```

**3. OAuth Flow Implementation**

```typescript
// app/auth/oauth/[provider]/route.ts
export async function GET(request: NextRequest, { params }: { params: { provider: string } }) {
  const provider = oauthProviders[params.provider];
  if (!provider) {
    return NextResponse.json({ error: 'Invalid provider' }, { status: 400 });
  }

  // Generate PKCE challenge
  const codeVerifier = generateCodeVerifier();
  const codeChallenge = await generateCodeChallenge(codeVerifier);
  const state = generateSecureToken();

  // Store PKCE and state in secure cookie
  const oauthState = {
    codeVerifier,
    state,
    provider: params.provider,
    redirectTo: request.nextUrl.searchParams.get('redirect') || '/',
  };

  const response = NextResponse.redirect(buildAuthorizationUrl(provider, codeChallenge, state));
  response.cookies.set('oauth-state', JSON.stringify(oauthState), {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax', // Required for OAuth redirects
    maxAge: 600, // 10 minutes
  });

  return response;
}
```

### 2. Single Sign-On (SSO) Architecture

#### SAML 2.0 Integration

```mermaid
sequenceDiagram
    participant U as User
    participant SP as Service Provider (Our App)
    participant IDP as Identity Provider (SAML)
    participant S as Session Service

    U->>SP: Access protected resource
    SP->>SP: Check session
    SP->>IDP: SAML AuthnRequest
    IDP->>U: Present login form
    U->>IDP: Authenticate
    IDP->>SP: SAML Response + Assertion
    SP->>SP: Validate SAML assertion
    SP->>S: Create enhanced session
    S->>SP: Return session token
    SP->>U: Grant access + set session
```

**Implementation Components**:

```typescript
// lib/saml-config.ts
interface SAMLConfig {
  entityId: string;
  ssoUrl: string;
  sloUrl: string;
  certificate: string;
  attributeMapping: {
    email: string;
    firstName: string;
    lastName: string;
    roles: string;
    department: string;
  };
}

// Enhanced session creation for SSO
async function createSSOSession(samlAssertion: SAMLAssertion): Promise<string> {
  const userData = {
    username: samlAssertion.email,
    email: samlAssertion.email,
    firstName: samlAssertion.firstName,
    lastName: samlAssertion.lastName,
    provider: 'saml',
    roles: samlAssertion.roles || [],
    department: samlAssertion.department,
    ssoSessionId: samlAssertion.sessionId,
  };

  return await createSession(userData);
}
```

### 3. Role-Based Access Control (RBAC) Implementation

#### RBAC Architecture Design

```mermaid
graph TB
    subgraph "RBAC Core Components"
        A[Users] --> B[User-Role Assignments]
        C[Roles] --> D[Role-Permission Assignments]
        E[Permissions] --> F[Resource-Action Pairs]
        B --> C
        D --> E
    end

    subgraph "Permission Evaluation"
        G[Request Context] --> H[Extract User Roles]
        H --> I[Resolve Role Permissions]
        I --> J[Check Resource Access]
        J --> K[Grant/Deny Decision]
    end

    subgraph "Implementation Layers"
        L[Middleware RBAC]
        M[Component RBAC]
        N[API Route RBAC]
        O[Database Row-Level Security]
    end
```

#### 1. RBAC Data Models

```typescript
// types/rbac.ts
interface User {
  id: string;
  email: string;
  roles: Role[];
  organizationId?: string;
}

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  organizationId?: string; // For multi-tenant RBAC
}

interface Permission {
  id: string;
  resource: string; // e.g., 'devices', 'earnings', 'users'
  action: string; // e.g., 'read', 'write', 'delete', 'admin'
  conditions?: PermissionCondition[]; // e.g., 'own_resources_only'
}

interface PermissionCondition {
  field: string; // e.g., 'userId', 'organizationId'
  operator: 'eq' | 'in' | 'contains';
  value: any;
}

// Enhanced session with RBAC
interface RBACSessionPayload extends EnhancedSessionPayload {
  roles: string[];
  permissions: string[];
  organizationId?: string;
  effectivePermissions?: ResolvedPermission[];
}

interface ResolvedPermission {
  resource: string;
  actions: string[];
  conditions: PermissionCondition[];
}
```

#### 2. Permission Resolution Engine

```typescript
// lib/rbac/permission-resolver.ts
class PermissionResolver {
  async resolveUserPermissions(userId: string): Promise<ResolvedPermission[]> {
    // 1. Get user roles
    const userRoles = await this.getUserRoles(userId);

    // 2. Aggregate permissions from all roles
    const permissions = await this.aggregateRolePermissions(userRoles);

    // 3. Resolve conflicts (most permissive wins)
    return this.resolvePermissionConflicts(permissions);
  }

  async checkPermission(
    userId: string,
    resource: string,
    action: string,
    context?: Record<string, any>,
  ): Promise<boolean> {
    const permissions = await this.resolveUserPermissions(userId);

    const relevantPermission = permissions.find((p) => p.resource === resource);
    if (!relevantPermission || !relevantPermission.actions.includes(action)) {
      return false;
    }

    // Evaluate conditions
    return this.evaluateConditions(relevantPermission.conditions, context);
  }

  private evaluateConditions(
    conditions: PermissionCondition[],
    context: Record<string, any>,
  ): boolean {
    return conditions.every((condition) => {
      const contextValue = context[condition.field];

      switch (condition.operator) {
        case 'eq':
          return contextValue === condition.value;
        case 'in':
          return Array.isArray(condition.value) && condition.value.includes(contextValue);
        case 'contains':
          return Array.isArray(contextValue) && contextValue.includes(condition.value);
        default:
          return false;
      }
    });
  }
}
```

#### 3. RBAC Middleware Implementation

```typescript
// middleware/rbac.ts
export function createRBACMiddleware(requiredPermission: { resource: string; action: string }) {
  return async (request: NextRequest) => {
    const session = await getSession();
    if (!session) {
      return NextResponse.redirect(new URL('/auth/signin', request.url));
    }

    const permissionResolver = new PermissionResolver();
    const hasPermission = await permissionResolver.checkPermission(
      session.userId!,
      requiredPermission.resource,
      requiredPermission.action,
      {
        userId: session.userId,
        organizationId: session.organizationId,
        // Add request-specific context
        requestPath: request.nextUrl.pathname,
      },
    );

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    return NextResponse.next();
  };
}

// Usage in route handlers
export const middleware = createRBACMiddleware({
  resource: 'devices',
  action: 'read',
});
```

#### 4. Component-Level RBAC

```typescript
// hooks/use-rbac.ts
export function useRBAC() {
  const session = useSession();
  const [permissions, setPermissions] = useState<ResolvedPermission[]>([]);

  useEffect(() => {
    if (session?.userId) {
      const resolver = new PermissionResolver();
      resolver.resolveUserPermissions(session.userId).then(setPermissions);
    }
  }, [session?.userId]);

  const hasPermission = useCallback((resource: string, action: string) => {
    const permission = permissions.find(p => p.resource === resource);
    return permission?.actions.includes(action) || false;
  }, [permissions]);

  const canAccess = useCallback((resource: string, action: string) => {
    return hasPermission(resource, action);
  }, [hasPermission]);

  return { hasPermission, canAccess, permissions };
}

// Component usage
function DeviceManagement() {
  const { canAccess } = useRBAC();

  if (!canAccess('devices', 'read')) {
    return <AccessDenied />;
  }

  return (
    <div>
      <DeviceList />
      {canAccess('devices', 'write') && <AddDeviceButton />}
      {canAccess('devices', 'delete') && <DeleteDeviceButton />}
    </div>
  );
}
```

### 4. Multi-Tenant Architecture Considerations

#### Tenant Isolation Strategy

```typescript
// Enhanced session for multi-tenancy
interface MultiTenantSessionPayload extends RBACSessionPayload {
  tenantId: string;
  tenantRole: 'admin' | 'member' | 'viewer';
  crossTenantAccess?: string[]; // For users with access to multiple tenants
}

// Tenant-aware permission checking
async function checkTenantPermission(
  userId: string,
  tenantId: string,
  resource: string,
  action: string,
): Promise<boolean> {
  // 1. Verify user has access to tenant
  const tenantAccess = await getUserTenantAccess(userId, tenantId);
  if (!tenantAccess) return false;

  // 2. Check tenant-scoped permissions
  return await checkPermission(userId, resource, action, { tenantId });
}
```

### 5. Security Monitoring & Audit Trail

#### Enhanced Logging for Advanced Auth

```typescript
// lib/audit-logger.ts
interface AuditEvent {
  eventType: 'auth' | 'permission' | 'session' | 'oauth' | 'sso';
  userId?: string;
  sessionId?: string;
  action: string;
  resource?: string;
  result: 'success' | 'failure' | 'denied';
  metadata: Record<string, any>;
  timestamp: Date;
  ipAddress: string;
  userAgent: string;
}

class AuditLogger {
  async logAuthEvent(event: Partial<AuditEvent>) {
    const auditEvent: AuditEvent = {
      eventType: 'auth',
      action: 'unknown',
      result: 'failure',
      metadata: {},
      timestamp: new Date(),
      ipAddress: 'unknown',
      userAgent: 'unknown',
      ...event,
    };

    // Log to multiple destinations
    await Promise.all([
      this.logToDatabase(auditEvent),
      this.logToSIEM(auditEvent),
      this.logToMetrics(auditEvent),
    ]);
  }
}
```

---

_This enhanced documentation provides a comprehensive roadmap for evolving the authentication system to support enterprise-grade features while maintaining security and performance standards._
